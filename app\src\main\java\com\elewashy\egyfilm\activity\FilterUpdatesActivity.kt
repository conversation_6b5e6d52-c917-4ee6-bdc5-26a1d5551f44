package com.elewashy.egyfilm.activity

import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.lifecycleScope
import com.elewashy.egyfilm.R
import com.elewashy.egyfilm.fragment.AdBlocker
import com.elewashy.egyfilm.fragment.ValidLinkChecker
import com.elewashy.egyfilm.fragment.OpenLinkValidator
import com.elewashy.egyfilm.ui.theme.EgyFilmTheme
import com.elewashy.egyfilm.ui.components.EgyFilmTopAppBar
import com.elewashy.egyfilm.ui.components.FilterCard
import com.elewashy.egyfilm.ui.components.ProgressButton
import com.elewashy.egyfilm.ui.components.SectionTitle
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*

data class FilterUpdateState(
    val isLoading: Boolean = false,
    val status: String? = null,
    val lastUpdate: String = ""
)

class FilterUpdatesActivity : ComponentActivity() {

    private lateinit var adBlocker: AdBlocker
    private lateinit var validLinkChecker: ValidLinkChecker
    private lateinit var openLinkValidator: OpenLinkValidator

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize filter instances
        adBlocker = AdBlocker.getInstance(this)
        validLinkChecker = ValidLinkChecker.getInstance(this)
        openLinkValidator = OpenLinkValidator.getInstance(this)

        setContent {
            EgyFilmTheme {
                FilterUpdatesScreen(
                    onBackClick = { finish() },
                    onUpdateAdBlocker = { updateAdBlockerFilter() },
                    onUpdateValidLinks = { updateValidLinksFilter() },
                    onUpdateOpenLinks = { updateOpenLinksFilter() },
                    onUpdateAllFilters = { updateAllFilters() }
                )
            }
        }
    }

@Composable
fun FilterUpdatesScreen(
    onBackClick: () -> Unit,
    onUpdateAdBlocker: () -> Unit,
    onUpdateValidLinks: () -> Unit,
    onUpdateOpenLinks: () -> Unit,
    onUpdateAllFilters: () -> Unit
) {
    val context = LocalContext.current

    // State for each filter
    var adBlockerState by remember { mutableStateOf(FilterUpdateState()) }
    var validLinksState by remember { mutableStateOf(FilterUpdateState()) }
    var openLinksState by remember { mutableStateOf(FilterUpdateState()) }
    var isUpdatingAll by remember { mutableStateOf(false) }

    // Load last updated times
    LaunchedEffect(Unit) {
        val sharedPrefs = context.getSharedPreferences("FilterUpdateTimes", ComponentActivity.MODE_PRIVATE)
        val dateFormat = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())

        // Load AdBlocker update times
        val adBlockerTime = sharedPrefs.getLong("adBlockerLastUpdate", 0)
        val adBlockerFrequentTime = sharedPrefs.getLong("adBlockerFrequentLastUpdate", 0)
        val latestAdBlockerTime = maxOf(adBlockerTime, adBlockerFrequentTime)

        adBlockerState = adBlockerState.copy(
            lastUpdate = if (latestAdBlockerTime > 0) {
                context.getString(R.string.last_updated, dateFormat.format(Date(latestAdBlockerTime)))
            } else {
                context.getString(R.string.never_updated)
            }
        )

        // Load ValidLinks update time
        val validLinksTime = sharedPrefs.getLong("validLinksLastUpdate", 0)
        validLinksState = validLinksState.copy(
            lastUpdate = if (validLinksTime > 0) {
                context.getString(R.string.last_updated, dateFormat.format(Date(validLinksTime)))
            } else {
                context.getString(R.string.never_updated)
            }
        )

        // Load OpenLinks update time
        val openLinksTime = sharedPrefs.getLong("openLinksLastUpdate", 0)
        openLinksState = openLinksState.copy(
            lastUpdate = if (openLinksTime > 0) {
                context.getString(R.string.last_updated, dateFormat.format(Date(openLinksTime)))
            } else {
                context.getString(R.string.never_updated)
            }
        )
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF1A1A1A))
    ) {
        // Top App Bar
        EgyFilmTopAppBar(
            title = stringResource(R.string.filter_updates),
            onBackClick = onBackClick
        )

        // Main Content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(16.dp)
        ) {
            SectionTitle(
                title = stringResource(R.string.filter_updates),
                icon = Icons.Default.Settings
            )

            // AdBlocker Filter
            FilterCard(
                title = stringResource(R.string.adblocker_filter),
                lastUpdate = adBlockerState.lastUpdate,
                status = adBlockerState.status,
                isLoading = adBlockerState.isLoading,
                onUpdateClick = onUpdateAdBlocker
            )

            // Valid Links Filter
            FilterCard(
                title = stringResource(R.string.valid_filter),
                lastUpdate = validLinksState.lastUpdate,
                status = validLinksState.status,
                isLoading = validLinksState.isLoading,
                onUpdateClick = onUpdateValidLinks
            )

            // Open Links Filter
            FilterCard(
                title = stringResource(R.string.open_filter),
                lastUpdate = openLinksState.lastUpdate,
                status = openLinksState.status,
                isLoading = openLinksState.isLoading,
                onUpdateClick = onUpdateOpenLinks
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Update All Filters Button
            ProgressButton(
                text = stringResource(R.string.update_all_filters),
                isLoading = isUpdatingAll,
                onClick = onUpdateAllFilters,
                icon = Icons.Default.Refresh,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}


    
    private fun updateAdBlockerFilter() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // Force update by clearing time constraints temporarily
                clearAdBlockerTimeConstraints()

                // Update both lists
                adBlocker.updateEasyList()
                adBlocker.updateFrequentList()

                // Save update time
                saveUpdateTime("adBlockerLastUpdate")

                withContext(Dispatchers.Main) {
                    Toast.makeText(this@FilterUpdatesActivity, "AdBlocker Filter ${getString(R.string.update_success)}", Toast.LENGTH_SHORT).show()
                }

            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    Toast.makeText(this@FilterUpdatesActivity, "AdBlocker Filter ${getString(R.string.update_failed)}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    private fun updateValidLinksFilter() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                validLinkChecker.updateValidLinks()
                saveUpdateTime("validLinksLastUpdate")

                withContext(Dispatchers.Main) {
                    Toast.makeText(this@FilterUpdatesActivity, "Valid Links Filter ${getString(R.string.update_success)}", Toast.LENGTH_SHORT).show()
                }

            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    Toast.makeText(this@FilterUpdatesActivity, "Valid Links Filter ${getString(R.string.update_failed)}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    private fun updateOpenLinksFilter() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                openLinkValidator.updateOpenLinks()
                saveUpdateTime("openLinksLastUpdate")

                withContext(Dispatchers.Main) {
                    Toast.makeText(this@FilterUpdatesActivity, "Open Links Filter ${getString(R.string.update_success)}", Toast.LENGTH_SHORT).show()
                }

            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    Toast.makeText(this@FilterUpdatesActivity, "Open Links Filter ${getString(R.string.update_failed)}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    private fun updateAllFilters() {
        lifecycleScope.launch(Dispatchers.IO) {
            var successCount = 0
            var totalCount = 3

            // Update AdBlocker
            try {
                clearAdBlockerTimeConstraints()
                adBlocker.updateEasyList()
                adBlocker.updateFrequentList()
                saveUpdateTime("adBlockerLastUpdate")
                successCount++
            } catch (e: Exception) {
                // Handle error silently for batch update
            }

            // Update ValidLinks
            try {
                validLinkChecker.updateValidLinks()
                saveUpdateTime("validLinksLastUpdate")
                successCount++
            } catch (e: Exception) {
                // Handle error silently for batch update
            }

            // Update OpenLinks
            try {
                openLinkValidator.updateOpenLinks()
                saveUpdateTime("openLinksLastUpdate")
                successCount++
            } catch (e: Exception) {
                // Handle error silently for batch update
            }

            withContext(Dispatchers.Main) {
                // Show result toast
                val message = if (successCount == totalCount) {
                    "All filters updated successfully!"
                } else {
                    "$successCount of $totalCount filters updated successfully"
                }
                Toast.makeText(this@FilterUpdatesActivity, message, Toast.LENGTH_LONG).show()
            }
        }
    }
    
    private fun clearAdBlockerTimeConstraints() {
        // Clear time constraints to force update
        val adBlockerPrefs = getSharedPreferences("AdBlockerPrefs", MODE_PRIVATE)
        adBlockerPrefs.edit().apply {
            putLong("lastUpdateTime", 0)
            putLong("lastFrequentUpdateTime", 0)
            apply()
        }
    }
    
    private fun saveUpdateTime(key: String) {
        val sharedPrefs = getSharedPreferences("FilterUpdateTimes", MODE_PRIVATE)
        sharedPrefs.edit().putLong(key, System.currentTimeMillis()).apply()
    }
}
