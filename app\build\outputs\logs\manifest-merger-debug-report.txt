-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:105:9-113:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:109:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:107:13-60
	android:exported
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:108:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:106:13-62
manifest
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:2:1-117:12
INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:2:1-117:12
INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:2:1-117:12
INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:2:1-117:12
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\12036fb2baccc4e5d772dbf0b3eca1b2\transformed\viewbinding-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e607c6d6b387b2dd350f81ff86f42b1c\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\be3d0efae3f8d2a331bba07c1d812ee9\transformed\constraintlayout-2.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eff9cff50879f53ed69fa4d5cb7d75fe\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\34f67e625150b5b83f2cf9d8db5420ea\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e4b9871c1d0d0014cf7374c6c8a4407\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d15897909ae325022c6fc698bcd47cb0\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9457c950ea951481c81d9da47623cc09\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\095a42543576dfd07f5d707cc30985e2\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2204aaa80f3200560822cfefbb498cc4\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e06136e29805ff114ddb0183f6fec56b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\713b4f8142baa8330e2ce9a8641a030c\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1823d3d8166c82ecfc7194fc993dc29d\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4e6560aef97eaf2acf7f2dc8b113c17d\transformed\activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9bc8f22ad8335f20a822c73efe6bc42\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\94fd5b29a1b1067b9fdcc671e5171cbb\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3fa168d136f2e8bd29de3c9c0bec65fa\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3d2eb0c585bd245e21308e9fd2f8bfac\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\db67b6db6958311173311bd27dd4367c\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\125ede2089e2e46d7c1e8a8a7679511b\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6be67e632ae5b8df80592dcb33ff70f\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d639710bd033afcdc83f4fb75f6a232a\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c8b185b32c96f96a1ac88f4c5bf154b7\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a198f45618b02ca82c3d7efc50bbffa4\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c80744c1d635c53ee3e339176215f2ab\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\12729c48cf23d4b88b5811047f60f643\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\61b8d3728d8b4e688c12fa8583709a66\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b89e4cad5821cad02071472914b1b27a\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1f6943cae19452808ea696db8488b38a\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f9749fe67cbf8b0246a870c71bed68b\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07f1a3826fbd9d9856416e7b132a9148\transformed\activity-compose-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bcf587d6fa9624847b668b55361187ad\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd18db171d2d1cb14efa0af25b8fd4e4\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a1583c19c31c3f64ff865952ae4f00f4\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6623a65ab37edc039c3039359f04d7ec\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a646e65d740e26c503091cb704ccfe0c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\382d85fbc0d4f580a5754540f00dbd2a\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d6d5c10e3216e070c28ed555bc724fd6\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32c8b0ba172008d8eeea6670b8d79073\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\61c52b35d089cb42e1a099f440b106c7\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6e3054a35f7a93d750f64846db0d45c7\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aef3e45cefd5a0385560ed0044e94728\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9a219d43ef97950d15f1477fdf64108\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fe8bc492cd3f0f79002893094785ec38\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebde6d0709c3f176d8a6a2108d3042a0\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8520b2fb67c5b87a12c40f5fde46b0df\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e3452cbb5fb9d54fc4fa1b7cdc4b5cdc\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\85a286c58a4bb79c7788251441d2da5f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f5f410b597bac6c92672546875a6b56c\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a93774641954b2ecb01ce83cc9c6806f\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90955186cbb9e83a7c0392913b52f229\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cb68677d5b58da1e6c8083d3ae8e5f5b\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c608acef2bf627cb353c1a031e44556d\transformed\play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9e0616ec103912d4336c7a2fadd91f5\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fd881a00c5f9961a0530a273812ef638\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\758e3754bbbe22f772f785c7605b2b77\transformed\activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d309d463e736c4859f6205393e6777ab\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f583a4b8e49fcbbb88e4c2de84394c10\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ca61a89072efe831538c03c6d782ee40\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0f749684a22ccf89dd37095fa4e93baa\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a772f72b817ff503bba597dae213fe5\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d91d2ea6a48cab51dcae918cfbce0ce\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b76a80c5f387f896e862cd1d24c64833\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5fd0e7035440ea54404eafef4824528a\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a84053f83145b24d04f3a087520c638\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5da2ef4516be68e8a5a9bb24b49ec3da\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc421b5416f9fd7af2e9804050a57474\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\244ae3c9fce65587cb9a4c285f2b0f67\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a5f18a5bf5f010f03c46ca7ba8404c68\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a4a1e7665b2d61b12f676fc457463176\transformed\core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\34684314edf5cae4fde700e8f2fb08b0\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\56fd1577e407ef7ee5ae30abba338964\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1e540d0b65f10b9dd8f9c108b3009ffc\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d56b83892d56a9c8832f15c5db1e09b3\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\78e73f7aa25c0b9a11ae7ab24c0cc6ee\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1870e8c0519cbce6fdcbbd27fb44a2b1\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3311ca7cd5e976d5009a4e93c9d50728\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3090166317501ebe597a2187c9cbc3a3\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6855d7781d2ed778f3d393690a381fe2\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f1b1cd34ec6a3d7b62b59f96c40c93c4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f97067c1253a4527382681a715e829c1\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f0c0379d0754c4bb520273c7328c4ffb\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a41e5e772257c9740ee588952f1c5e8f\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f618a655bbfe7905b483e251ca9e08b9\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\374157b07880332115dc975b0942eb0e\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:5:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2204aaa80f3200560822cfefbb498cc4\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2204aaa80f3200560822cfefbb498cc4\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:5:22-76
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2204aaa80f3200560822cfefbb498cc4\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2204aaa80f3200560822cfefbb498cc4\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:7:5-108
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:7:79-105
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:8:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:8:22-77
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:9:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:9:22-74
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:10:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:10:22-79
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:11:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:11:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:12:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:12:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_DATA_SYNC
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:13:5-86
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:13:22-84
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:14:5-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:14:22-80
uses-permission#android.permission.REQUEST_DELETE_PACKAGES
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:15:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:15:22-79
application
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:17:5-115:19
INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:17:5-115:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e607c6d6b387b2dd350f81ff86f42b1c\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e607c6d6b387b2dd350f81ff86f42b1c\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\be3d0efae3f8d2a331bba07c1d812ee9\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\be3d0efae3f8d2a331bba07c1d812ee9\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d15897909ae325022c6fc698bcd47cb0\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d15897909ae325022c6fc698bcd47cb0\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9457c950ea951481c81d9da47623cc09\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9457c950ea951481c81d9da47623cc09\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\095a42543576dfd07f5d707cc30985e2\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\095a42543576dfd07f5d707cc30985e2\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e06136e29805ff114ddb0183f6fec56b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e06136e29805ff114ddb0183f6fec56b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1f6943cae19452808ea696db8488b38a\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1f6943cae19452808ea696db8488b38a\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f9749fe67cbf8b0246a870c71bed68b\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f9749fe67cbf8b0246a870c71bed68b\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a1583c19c31c3f64ff865952ae4f00f4\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a1583c19c31c3f64ff865952ae4f00f4\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fe8bc492cd3f0f79002893094785ec38\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fe8bc492cd3f0f79002893094785ec38\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cb68677d5b58da1e6c8083d3ae8e5f5b\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cb68677d5b58da1e6c8083d3ae8e5f5b\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c608acef2bf627cb353c1a031e44556d\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c608acef2bf627cb353c1a031e44556d\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\56fd1577e407ef7ee5ae30abba338964\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\56fd1577e407ef7ee5ae30abba338964\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3311ca7cd5e976d5009a4e93c9d50728\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3311ca7cd5e976d5009a4e93c9d50728\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f1b1cd34ec6a3d7b62b59f96c40c93c4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f1b1cd34ec6a3d7b62b59f96c40c93c4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:22:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:20:9-41
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:25:9-43
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:21:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:26:9-28
	android:icon
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:19:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:18:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:23:9-45
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:24:9-69
activity#com.elewashy.egyfilm.activity.SplashActivity
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:29:9-37:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:31:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:32:13-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:30:13-52
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:33:13-36:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:34:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:34:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:35:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:35:27-74
activity#com.elewashy.egyfilm.activity.MainActivity
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:39:9-43:46
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:43:13-43
	android:exported
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:42:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:41:13-122
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:40:13-50
activity#com.elewashy.egyfilm.activity.DownloadActivity
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:46:9-55:20
	android:parentActivityName
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:50:13-64
	android:label
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:49:13-38
	android:exported
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:48:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:51:13-49
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:47:13-54
meta-data#android.app.lib_name
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:52:13-54:36
	android:value
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:54:17-33
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:53:17-52
activity#com.elewashy.egyfilm.activity.SettingsActivity
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:58:9-67:20
	android:parentActivityName
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:62:13-64
	android:label
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:61:13-45
	android:exported
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:60:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:63:13-49
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:59:13-54
activity#com.elewashy.egyfilm.activity.FilterUpdatesActivity
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:70:9-79:20
	android:parentActivityName
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:74:13-68
	android:label
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:73:13-51
	android:exported
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:72:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:75:13-49
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:71:13-59
service#com.elewashy.egyfilm.service.DownloadService
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:82:9-85:56
	android:exported
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:84:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:85:13-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:83:13-52
service#com.elewashy.egyfilm.service.MyFirebaseMessagingService
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:88:9-94:19
	android:exported
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:90:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:89:13-63
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:91:13-93:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:92:17-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:92:25-75
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:97:9-99:54
	android:resource
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:99:13-51
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:98:13-83
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:100:9-102:53
	android:value
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:102:13-50
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:101:13-89
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:110:13-112:58
	android:resource
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:112:17-55
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:111:17-67
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\12036fb2baccc4e5d772dbf0b3eca1b2\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\12036fb2baccc4e5d772dbf0b3eca1b2\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e607c6d6b387b2dd350f81ff86f42b1c\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e607c6d6b387b2dd350f81ff86f42b1c\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\be3d0efae3f8d2a331bba07c1d812ee9\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\be3d0efae3f8d2a331bba07c1d812ee9\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eff9cff50879f53ed69fa4d5cb7d75fe\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eff9cff50879f53ed69fa4d5cb7d75fe\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\34f67e625150b5b83f2cf9d8db5420ea\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\34f67e625150b5b83f2cf9d8db5420ea\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e4b9871c1d0d0014cf7374c6c8a4407\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e4b9871c1d0d0014cf7374c6c8a4407\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d15897909ae325022c6fc698bcd47cb0\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d15897909ae325022c6fc698bcd47cb0\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9457c950ea951481c81d9da47623cc09\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9457c950ea951481c81d9da47623cc09\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\095a42543576dfd07f5d707cc30985e2\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\095a42543576dfd07f5d707cc30985e2\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2204aaa80f3200560822cfefbb498cc4\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2204aaa80f3200560822cfefbb498cc4\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e06136e29805ff114ddb0183f6fec56b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e06136e29805ff114ddb0183f6fec56b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\713b4f8142baa8330e2ce9a8641a030c\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\713b4f8142baa8330e2ce9a8641a030c\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1823d3d8166c82ecfc7194fc993dc29d\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1823d3d8166c82ecfc7194fc993dc29d\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4e6560aef97eaf2acf7f2dc8b113c17d\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4e6560aef97eaf2acf7f2dc8b113c17d\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9bc8f22ad8335f20a822c73efe6bc42\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9bc8f22ad8335f20a822c73efe6bc42\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\94fd5b29a1b1067b9fdcc671e5171cbb\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\94fd5b29a1b1067b9fdcc671e5171cbb\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3fa168d136f2e8bd29de3c9c0bec65fa\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3fa168d136f2e8bd29de3c9c0bec65fa\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3d2eb0c585bd245e21308e9fd2f8bfac\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3d2eb0c585bd245e21308e9fd2f8bfac\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\db67b6db6958311173311bd27dd4367c\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\db67b6db6958311173311bd27dd4367c\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\125ede2089e2e46d7c1e8a8a7679511b\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\125ede2089e2e46d7c1e8a8a7679511b\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6be67e632ae5b8df80592dcb33ff70f\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6be67e632ae5b8df80592dcb33ff70f\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d639710bd033afcdc83f4fb75f6a232a\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d639710bd033afcdc83f4fb75f6a232a\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c8b185b32c96f96a1ac88f4c5bf154b7\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c8b185b32c96f96a1ac88f4c5bf154b7\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a198f45618b02ca82c3d7efc50bbffa4\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a198f45618b02ca82c3d7efc50bbffa4\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c80744c1d635c53ee3e339176215f2ab\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c80744c1d635c53ee3e339176215f2ab\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\12729c48cf23d4b88b5811047f60f643\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\12729c48cf23d4b88b5811047f60f643\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\61b8d3728d8b4e688c12fa8583709a66\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\61b8d3728d8b4e688c12fa8583709a66\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b89e4cad5821cad02071472914b1b27a\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b89e4cad5821cad02071472914b1b27a\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1f6943cae19452808ea696db8488b38a\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1f6943cae19452808ea696db8488b38a\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f9749fe67cbf8b0246a870c71bed68b\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f9749fe67cbf8b0246a870c71bed68b\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07f1a3826fbd9d9856416e7b132a9148\transformed\activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07f1a3826fbd9d9856416e7b132a9148\transformed\activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bcf587d6fa9624847b668b55361187ad\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bcf587d6fa9624847b668b55361187ad\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd18db171d2d1cb14efa0af25b8fd4e4\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd18db171d2d1cb14efa0af25b8fd4e4\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a1583c19c31c3f64ff865952ae4f00f4\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a1583c19c31c3f64ff865952ae4f00f4\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6623a65ab37edc039c3039359f04d7ec\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6623a65ab37edc039c3039359f04d7ec\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a646e65d740e26c503091cb704ccfe0c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a646e65d740e26c503091cb704ccfe0c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\382d85fbc0d4f580a5754540f00dbd2a\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\382d85fbc0d4f580a5754540f00dbd2a\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d6d5c10e3216e070c28ed555bc724fd6\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d6d5c10e3216e070c28ed555bc724fd6\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32c8b0ba172008d8eeea6670b8d79073\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32c8b0ba172008d8eeea6670b8d79073\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\61c52b35d089cb42e1a099f440b106c7\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\61c52b35d089cb42e1a099f440b106c7\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6e3054a35f7a93d750f64846db0d45c7\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6e3054a35f7a93d750f64846db0d45c7\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aef3e45cefd5a0385560ed0044e94728\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aef3e45cefd5a0385560ed0044e94728\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9a219d43ef97950d15f1477fdf64108\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9a219d43ef97950d15f1477fdf64108\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fe8bc492cd3f0f79002893094785ec38\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fe8bc492cd3f0f79002893094785ec38\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebde6d0709c3f176d8a6a2108d3042a0\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebde6d0709c3f176d8a6a2108d3042a0\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8520b2fb67c5b87a12c40f5fde46b0df\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8520b2fb67c5b87a12c40f5fde46b0df\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e3452cbb5fb9d54fc4fa1b7cdc4b5cdc\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e3452cbb5fb9d54fc4fa1b7cdc4b5cdc\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\85a286c58a4bb79c7788251441d2da5f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\85a286c58a4bb79c7788251441d2da5f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f5f410b597bac6c92672546875a6b56c\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f5f410b597bac6c92672546875a6b56c\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a93774641954b2ecb01ce83cc9c6806f\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a93774641954b2ecb01ce83cc9c6806f\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90955186cbb9e83a7c0392913b52f229\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90955186cbb9e83a7c0392913b52f229\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cb68677d5b58da1e6c8083d3ae8e5f5b\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cb68677d5b58da1e6c8083d3ae8e5f5b\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c608acef2bf627cb353c1a031e44556d\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c608acef2bf627cb353c1a031e44556d\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9e0616ec103912d4336c7a2fadd91f5\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9e0616ec103912d4336c7a2fadd91f5\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fd881a00c5f9961a0530a273812ef638\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fd881a00c5f9961a0530a273812ef638\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\758e3754bbbe22f772f785c7605b2b77\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\758e3754bbbe22f772f785c7605b2b77\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d309d463e736c4859f6205393e6777ab\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d309d463e736c4859f6205393e6777ab\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f583a4b8e49fcbbb88e4c2de84394c10\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f583a4b8e49fcbbb88e4c2de84394c10\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ca61a89072efe831538c03c6d782ee40\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ca61a89072efe831538c03c6d782ee40\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0f749684a22ccf89dd37095fa4e93baa\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0f749684a22ccf89dd37095fa4e93baa\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a772f72b817ff503bba597dae213fe5\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a772f72b817ff503bba597dae213fe5\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d91d2ea6a48cab51dcae918cfbce0ce\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d91d2ea6a48cab51dcae918cfbce0ce\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b76a80c5f387f896e862cd1d24c64833\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b76a80c5f387f896e862cd1d24c64833\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5fd0e7035440ea54404eafef4824528a\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5fd0e7035440ea54404eafef4824528a\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a84053f83145b24d04f3a087520c638\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a84053f83145b24d04f3a087520c638\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5da2ef4516be68e8a5a9bb24b49ec3da\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5da2ef4516be68e8a5a9bb24b49ec3da\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc421b5416f9fd7af2e9804050a57474\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc421b5416f9fd7af2e9804050a57474\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\244ae3c9fce65587cb9a4c285f2b0f67\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\244ae3c9fce65587cb9a4c285f2b0f67\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a5f18a5bf5f010f03c46ca7ba8404c68\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a5f18a5bf5f010f03c46ca7ba8404c68\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a4a1e7665b2d61b12f676fc457463176\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a4a1e7665b2d61b12f676fc457463176\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\34684314edf5cae4fde700e8f2fb08b0\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\34684314edf5cae4fde700e8f2fb08b0\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\56fd1577e407ef7ee5ae30abba338964\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\56fd1577e407ef7ee5ae30abba338964\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1e540d0b65f10b9dd8f9c108b3009ffc\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1e540d0b65f10b9dd8f9c108b3009ffc\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d56b83892d56a9c8832f15c5db1e09b3\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d56b83892d56a9c8832f15c5db1e09b3\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\78e73f7aa25c0b9a11ae7ab24c0cc6ee\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\78e73f7aa25c0b9a11ae7ab24c0cc6ee\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1870e8c0519cbce6fdcbbd27fb44a2b1\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1870e8c0519cbce6fdcbbd27fb44a2b1\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3311ca7cd5e976d5009a4e93c9d50728\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3311ca7cd5e976d5009a4e93c9d50728\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3090166317501ebe597a2187c9cbc3a3\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3090166317501ebe597a2187c9cbc3a3\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6855d7781d2ed778f3d393690a381fe2\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6855d7781d2ed778f3d393690a381fe2\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f1b1cd34ec6a3d7b62b59f96c40c93c4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f1b1cd34ec6a3d7b62b59f96c40c93c4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f97067c1253a4527382681a715e829c1\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f97067c1253a4527382681a715e829c1\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f0c0379d0754c4bb520273c7328c4ffb\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f0c0379d0754c4bb520273c7328c4ffb\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a41e5e772257c9740ee588952f1c5e8f\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a41e5e772257c9740ee588952f1c5e8f\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f618a655bbfe7905b483e251ca9e08b9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f618a655bbfe7905b483e251ca9e08b9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\374157b07880332115dc975b0942eb0e\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\374157b07880332115dc975b0942eb0e\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2204aaa80f3200560822cfefbb498cc4\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2204aaa80f3200560822cfefbb498cc4\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:22-65
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2204aaa80f3200560822cfefbb498cc4\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2204aaa80f3200560822cfefbb498cc4\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e06136e29805ff114ddb0183f6fec56b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e06136e29805ff114ddb0183f6fec56b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\56fd1577e407ef7ee5ae30abba338964\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\56fd1577e407ef7ee5ae30abba338964\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d15897909ae325022c6fc698bcd47cb0\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d15897909ae325022c6fc698bcd47cb0\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d15897909ae325022c6fc698bcd47cb0\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d15897909ae325022c6fc698bcd47cb0\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e06136e29805ff114ddb0183f6fec56b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e06136e29805ff114ddb0183f6fec56b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e06136e29805ff114ddb0183f6fec56b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1f6943cae19452808ea696db8488b38a\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1f6943cae19452808ea696db8488b38a\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1f6943cae19452808ea696db8488b38a\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f9749fe67cbf8b0246a870c71bed68b\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f9749fe67cbf8b0246a870c71bed68b\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f9749fe67cbf8b0246a870c71bed68b\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fe8bc492cd3f0f79002893094785ec38\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fe8bc492cd3f0f79002893094785ec38\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3311ca7cd5e976d5009a4e93c9d50728\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3311ca7cd5e976d5009a4e93c9d50728\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fe8bc492cd3f0f79002893094785ec38\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fe8bc492cd3f0f79002893094785ec38\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fe8bc492cd3f0f79002893094785ec38\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c608acef2bf627cb353c1a031e44556d\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c608acef2bf627cb353c1a031e44556d\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c608acef2bf627cb353c1a031e44556d\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.elewashy.egyfilm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.elewashy.egyfilm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\56fd1577e407ef7ee5ae30abba338964\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\56fd1577e407ef7ee5ae30abba338964\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\56fd1577e407ef7ee5ae30abba338964\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
