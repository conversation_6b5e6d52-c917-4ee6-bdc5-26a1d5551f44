{"logs": [{"outputFile": "com.elewashy.egyfilm.app-mergeDebugResources-62:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3d2eb0c585bd245e21308e9fd2f8bfac\\transformed\\foundation-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "208,209", "startColumns": "4,4", "startOffsets": "20254,20342", "endColumns": "87,94", "endOffsets": "20337,20432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\34f67e625150b5b83f2cf9d8db5420ea\\transformed\\appcompat-1.7.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,435,541,646,732,842,963,1043,1120,1211,1304,1399,1493,1593,1686,1781,1889,1980,2071,2154,2268,2376,2476,2590,2697,2805,2965,19478", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "430,536,641,727,837,958,1038,1115,1206,1299,1394,1488,1588,1681,1776,1884,1975,2066,2149,2263,2371,2471,2585,2692,2800,2960,3059,19557"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c608acef2bf627cb353c1a031e44556d\\transformed\\play-services-basement-18.3.0\\res\\values-bg\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5841", "endColumns": "137", "endOffsets": "5974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\012d12faf62837152fa164df8f2637b0\\transformed\\core-1.15.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "38,39,40,41,42,43,44,204", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3537,3634,3744,3846,3947,4054,4159,19880", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "3629,3739,3841,3942,4049,4154,4273,19976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e607c6d6b387b2dd350f81ff86f42b1c\\transformed\\material-1.12.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,423,506,628,738,833,966,1055,1118,1184,1281,1361,1423,1512,1575,1640,1699,1772,1835,1889,2017,2074,2136,2190,2263,2406,2490,2568,2661,2743,2831,2967,3055,3143,3279,3364,3441,3494,3545,3611,3686,3762,3833,3912,3989,4065,4142,4216,4328,4419,4494,4585,4677,4751,4838,4929,4984,5066,5132,5215,5301,5363,5427,5490,5560,5677,5789,5900,6010,6067,6122,6208,6299,6375", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,78,82,121,109,94,132,88,62,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,77,92,81,87,135,87,87,135,84,76,52,50,65,74,75,70,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85,90,75,78", "endOffsets": "260,339,418,501,623,733,828,961,1050,1113,1179,1276,1356,1418,1507,1570,1635,1694,1767,1830,1884,2012,2069,2131,2185,2258,2401,2485,2563,2656,2738,2826,2962,3050,3138,3274,3359,3436,3489,3540,3606,3681,3757,3828,3907,3984,4060,4137,4211,4323,4414,4489,4580,4672,4746,4833,4924,4979,5061,5127,5210,5296,5358,5422,5485,5555,5672,5784,5895,6005,6062,6117,6203,6294,6370,6449"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,71,72,73,74,77,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,196,200,201,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3064,3143,3222,3305,3427,4278,4373,4506,7388,7451,7517,7614,7861,14213,14302,14365,14430,14489,14562,14625,14679,14807,14864,14926,14980,15053,15196,15280,15358,15451,15533,15621,15757,15845,15933,16069,16154,16231,16284,16335,16401,16476,16552,16623,16702,16779,16855,16932,17006,17118,17209,17284,17375,17467,17541,17628,17719,17774,17856,17922,18005,18091,18153,18217,18280,18350,18467,18579,18690,18800,18857,19220,19562,19653,19801", "endLines": "5,33,34,35,36,37,45,46,47,71,72,73,74,77,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,196,200,201,203", "endColumns": "12,78,78,82,121,109,94,132,88,62,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,77,92,81,87,135,87,87,135,84,76,52,50,65,74,75,70,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85,90,75,78", "endOffsets": "310,3138,3217,3300,3422,3532,4368,4501,4590,7446,7512,7609,7689,7918,14297,14360,14425,14484,14557,14620,14674,14802,14859,14921,14975,15048,15191,15275,15353,15446,15528,15616,15752,15840,15928,16064,16149,16226,16279,16330,16396,16471,16547,16618,16697,16774,16850,16927,17001,17113,17204,17279,17370,17462,17536,17623,17714,17769,17851,17917,18000,18086,18148,18212,18275,18345,18462,18574,18685,18795,18852,18907,19301,19648,19724,19875"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d15897909ae325022c6fc698bcd47cb0\\transformed\\play-services-base-18.1.0\\res\\values-bg\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,459,590,697,860,991,1106,1211,1376,1484,1655,1789,1942,2104,2170,2225", "endColumns": "104,160,130,106,162,130,114,104,164,107,170,133,152,161,65,54,68", "endOffsets": "297,458,589,696,859,990,1105,1210,1375,1483,1654,1788,1941,2103,2169,2224,2293"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4791,4900,5065,5200,5311,5478,5613,5732,5979,6148,6260,6435,6573,6730,6896,6966,7025", "endColumns": "108,164,134,110,166,134,118,108,168,111,174,137,156,165,69,58,72", "endOffsets": "4895,5060,5195,5306,5473,5608,5727,5836,6143,6255,6430,6568,6725,6891,6961,7020,7093"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1823d3d8166c82ecfc7194fc993dc29d\\transformed\\material3-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,432,551,648,744,857,987,1108,1255,1339,1438,1534,1630,1743,1872,1976,2119,2262,2407,2595,2735,2862,2992,3126,3223,3320,3457,3592,3695,3800,3905,4050,4200,4308,4411,4498,4590,4685,4782,4872,4981,5061,5144,5244,5346,5442,5540,5628,5735,5835,5939,6058,6138,6248", "endColumns": "118,120,136,118,96,95,112,129,120,146,83,98,95,95,112,128,103,142,142,144,187,139,126,129,133,96,96,136,134,102,104,104,144,149,107,102,86,91,94,96,89,108,79,82,99,101,95,97,87,106,99,103,118,79,109,96", "endOffsets": "169,290,427,546,643,739,852,982,1103,1250,1334,1433,1529,1625,1738,1867,1971,2114,2257,2402,2590,2730,2857,2987,3121,3218,3315,3452,3587,3690,3795,3900,4045,4195,4303,4406,4493,4585,4680,4777,4867,4976,5056,5139,5239,5341,5437,5535,5623,5730,5830,5934,6053,6133,6243,6340"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7923,8042,8163,8300,8419,8516,8612,8725,8855,8976,9123,9207,9306,9402,9498,9611,9740,9844,9987,10130,10275,10463,10603,10730,10860,10994,11091,11188,11325,11460,11563,11668,11773,11918,12068,12176,12279,12366,12458,12553,12650,12740,12849,12929,13012,13112,13214,13310,13408,13496,13603,13703,13807,13926,14006,14116", "endColumns": "118,120,136,118,96,95,112,129,120,146,83,98,95,95,112,128,103,142,142,144,187,139,126,129,133,96,96,136,134,102,104,104,144,149,107,102,86,91,94,96,89,108,79,82,99,101,95,97,87,106,99,103,118,79,109,96", "endOffsets": "8037,8158,8295,8414,8511,8607,8720,8850,8971,9118,9202,9301,9397,9493,9606,9735,9839,9982,10125,10270,10458,10598,10725,10855,10989,11086,11183,11320,11455,11558,11663,11768,11913,12063,12171,12274,12361,12453,12548,12645,12735,12844,12924,13007,13107,13209,13305,13403,13491,13598,13698,13802,13921,14001,14111,14208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\bcf587d6fa9624847b668b55361187ad\\transformed\\ui-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,301,404,507,591,667,758,849,933,1000,1066,1150,1238,1310,1393,1462", "endColumns": "102,92,102,102,83,75,90,90,83,66,65,83,87,71,82,68,120", "endOffsets": "203,296,399,502,586,662,753,844,928,995,1061,1145,1233,1305,1388,1457,1578"}, "to": {"startLines": "48,49,68,69,70,75,76,192,193,194,195,197,198,202,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4595,4698,7098,7201,7304,7694,7770,18912,19003,19087,19154,19306,19390,19729,19981,20064,20133", "endColumns": "102,92,102,102,83,75,90,90,83,66,65,83,87,71,82,68,120", "endOffsets": "4693,4786,7196,7299,7383,7765,7856,18998,19082,19149,19215,19385,19473,19796,20059,20128,20249"}}]}]}