{"logs": [{"outputFile": "com.elewashy.egyfilm.app-mergeDebugResources-62:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e607c6d6b387b2dd350f81ff86f42b1c\\transformed\\material-1.12.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,262,351,439,521,616,705,807,917,1004,1064,1130,1226,1292,1353,1458,1522,1594,1652,1726,1788,1842,1955,2015,2076,2135,2213,2337,2418,2500,2600,2685,2770,2906,2987,3070,3201,3284,3370,3432,3486,3552,3629,3708,3779,3862,3931,4007,4088,4156,4260,4351,4429,4522,4619,4693,4772,4870,4930,5018,5084,5172,5260,5322,5390,5453,5519,5624,5730,5825,5930,5996,6054,6138,6227,6303", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,88,87,81,94,88,101,109,86,59,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83,88,75,72", "endOffsets": "257,346,434,516,611,700,802,912,999,1059,1125,1221,1287,1348,1453,1517,1589,1647,1721,1783,1837,1950,2010,2071,2130,2208,2332,2413,2495,2595,2680,2765,2901,2982,3065,3196,3279,3365,3427,3481,3547,3624,3703,3774,3857,3926,4002,4083,4151,4255,4346,4424,4517,4614,4688,4767,4865,4925,5013,5079,5167,5255,5317,5385,5448,5514,5619,5725,5820,5925,5991,6049,6133,6222,6298,6371"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,71,72,73,74,77,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,196,200,201,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3079,3168,3256,3338,3433,4242,4344,4454,7334,7394,7460,7556,7800,14236,14341,14405,14477,14535,14609,14671,14725,14838,14898,14959,15018,15096,15220,15301,15383,15483,15568,15653,15789,15870,15953,16084,16167,16253,16315,16369,16435,16512,16591,16662,16745,16814,16890,16971,17039,17143,17234,17312,17405,17502,17576,17655,17753,17813,17901,17967,18055,18143,18205,18273,18336,18402,18507,18613,18708,18813,18879,19242,19589,19678,19831", "endLines": "5,33,34,35,36,37,45,46,47,71,72,73,74,77,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,196,200,201,203", "endColumns": "12,88,87,81,94,88,101,109,86,59,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83,88,75,72", "endOffsets": "307,3163,3251,3333,3428,3517,4339,4449,4536,7389,7455,7551,7617,7856,14336,14400,14472,14530,14604,14666,14720,14833,14893,14954,15013,15091,15215,15296,15378,15478,15563,15648,15784,15865,15948,16079,16162,16248,16310,16364,16430,16507,16586,16657,16740,16809,16885,16966,17034,17138,17229,17307,17400,17497,17571,17650,17748,17808,17896,17962,18050,18138,18200,18268,18331,18397,18502,18608,18703,18808,18874,18932,19321,19673,19749,19899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\bcf587d6fa9624847b668b55361187ad\\transformed\\ui-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,294,388,485,571,653,749,836,922,988,1054,1144,1237,1314,1395,1463", "endColumns": "98,89,93,96,85,81,95,86,85,65,65,89,92,76,80,67,119", "endOffsets": "199,289,383,480,566,648,744,831,917,983,1049,1139,1232,1309,1390,1458,1578"}, "to": {"startLines": "48,49,68,69,70,75,76,192,193,194,195,197,198,202,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4541,4640,7057,7151,7248,7622,7704,18937,19024,19110,19176,19326,19416,19754,20005,20086,20154", "endColumns": "98,89,93,96,85,81,95,86,85,65,65,89,92,76,80,67,119", "endOffsets": "4635,4725,7146,7243,7329,7699,7795,19019,19105,19171,19237,19411,19504,19826,20081,20149,20269"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d15897909ae325022c6fc698bcd47cb0\\transformed\\play-services-base-18.1.0\\res\\values-ne\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,454,584,697,864,996,1102,1203,1379,1489,1649,1778,1922,2070,2132,2200", "endColumns": "106,153,129,112,166,131,105,100,175,109,159,128,143,147,61,67,87", "endOffsets": "299,453,583,696,863,995,1101,1202,1378,1488,1648,1777,1921,2069,2131,2199,2287"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4730,4841,4999,5133,5250,5421,5557,5667,5936,6116,6230,6394,6527,6675,6827,6893,6965", "endColumns": "110,157,133,116,170,135,109,104,179,113,163,132,147,151,65,71,91", "endOffsets": "4836,4994,5128,5245,5416,5552,5662,5767,6111,6225,6389,6522,6670,6822,6888,6960,7052"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\012d12faf62837152fa164df8f2637b0\\transformed\\core-1.15.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "38,39,40,41,42,43,44,204", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3522,3625,3728,3830,3936,4034,4134,19904", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3620,3723,3825,3931,4029,4129,4237,20000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1823d3d8166c82ecfc7194fc993dc29d\\transformed\\material3-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,303,419,547,646,741,853,1005,1126,1279,1363,1471,1569,1668,1780,1904,2017,2163,2306,2440,2605,2735,2887,3044,3173,3272,3367,3483,3607,3711,3830,3940,4086,4234,4344,4452,4527,4632,4737,4828,4923,5030,5117,5202,5303,5412,5507,5610,5697,5808,5907,6012,6147,6232,6336", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,90,94,106,86,84,100,108,94,102,86,110,98,104,134,84,103,93", "endOffsets": "179,298,414,542,641,736,848,1000,1121,1274,1358,1466,1564,1663,1775,1899,2012,2158,2301,2435,2600,2730,2882,3039,3168,3267,3362,3478,3602,3706,3825,3935,4081,4229,4339,4447,4522,4627,4732,4823,4918,5025,5112,5197,5298,5407,5502,5605,5692,5803,5902,6007,6142,6227,6331,6425"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7861,7990,8109,8225,8353,8452,8547,8659,8811,8932,9085,9169,9277,9375,9474,9586,9710,9823,9969,10112,10246,10411,10541,10693,10850,10979,11078,11173,11289,11413,11517,11636,11746,11892,12040,12150,12258,12333,12438,12543,12634,12729,12836,12923,13008,13109,13218,13313,13416,13503,13614,13713,13818,13953,14038,14142", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,90,94,106,86,84,100,108,94,102,86,110,98,104,134,84,103,93", "endOffsets": "7985,8104,8220,8348,8447,8542,8654,8806,8927,9080,9164,9272,9370,9469,9581,9705,9818,9964,10107,10241,10406,10536,10688,10845,10974,11073,11168,11284,11408,11512,11631,11741,11887,12035,12145,12253,12328,12433,12538,12629,12724,12831,12918,13003,13104,13213,13308,13411,13498,13609,13708,13813,13948,14033,14137,14231"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3d2eb0c585bd245e21308e9fd2f8bfac\\transformed\\foundation-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,88", "endOffsets": "135,224"}, "to": {"startLines": "208,209", "startColumns": "4,4", "startOffsets": "20274,20359", "endColumns": "84,88", "endOffsets": "20354,20443"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\34f67e625150b5b83f2cf9d8db5420ea\\transformed\\appcompat-1.7.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2254,2367,2477,2594,2761,2872", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2249,2362,2472,2589,2756,2867,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "312,421,532,640,731,838,958,1042,1121,1212,1305,1400,1494,1594,1687,1782,1876,1967,2058,2144,2257,2358,2461,2574,2684,2801,2968,19509", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "416,527,635,726,833,953,1037,1116,1207,1300,1395,1489,1589,1682,1777,1871,1962,2053,2139,2252,2353,2456,2569,2679,2796,2963,3074,19584"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c608acef2bf627cb353c1a031e44556d\\transformed\\play-services-basement-18.3.0\\res\\values-ne\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5772", "endColumns": "163", "endOffsets": "5931"}}]}]}