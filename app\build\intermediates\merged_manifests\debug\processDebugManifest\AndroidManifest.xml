<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.elewashy.egyfilm"
    android:versionCode="1"
    android:versionName="1.0.8" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="35" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- General permission -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" /> <!-- Specific permission for dataSync type -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> <!-- For installing APK files -->
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES" />
    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />

    <permission
        android:name="com.elewashy.egyfilm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.elewashy.egyfilm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- For managing packages -->
    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="true"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.egyfilm" >
        <activity
            android:name="com.elewashy.egyfilm.activity.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.egyfilm.NoActionBar.Fullscreen" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.elewashy.egyfilm.activity.MainActivity"
            android:configChanges="uiMode|keyboard|keyboardHidden|screenSize|screenLayout|smallestScreenSize|orientation"
            android:exported="true"
            android:launchMode="singleTop" /> <!-- Add launchMode singleTop -->


        <!-- Declare the Download Activity -->
        <activity
            android:name="com.elewashy.egyfilm.activity.DownloadActivity"
            android:exported="false"
            android:label="Downloads"
            android:parentActivityName="com.elewashy.egyfilm.activity.MainActivity"
            android:theme="@style/Theme.egyfilm" > <!-- Use the base theme which inherits NoActionBar -->
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>

        <!-- Declare the Settings Activity -->
        <activity
            android:name="com.elewashy.egyfilm.activity.SettingsActivity"
            android:exported="false"
            android:label="@string/settings"
            android:parentActivityName="com.elewashy.egyfilm.activity.MainActivity"
            android:theme="@style/Theme.egyfilm" >
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>

        <!-- Declare the Filter Updates Activity -->
        <activity
            android:name="com.elewashy.egyfilm.activity.FilterUpdatesActivity"
            android:exported="false"
            android:label="@string/filter_updates"
            android:parentActivityName="com.elewashy.egyfilm.activity.SettingsActivity"
            android:theme="@style/Theme.egyfilm" >
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>

        <!-- Declare the Download Service -->
        <service
            android:name="com.elewashy.egyfilm.service.DownloadService"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- Firebase Messaging Service -->
        <service
            android:name="com.elewashy.egyfilm.service.MyFirebaseMessagingService"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- Firebase default notification channel -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/ic_launcher" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="egyfilm_notifications" />

        <!-- FileProvider Declaration -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.elewashy.egyfilm.provider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <receiver
            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND" >
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>

            <meta-data
                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
                android:value="true" />
        </receiver>
        <!--
             FirebaseMessagingService performs security checks at runtime,
             but set to not exported to explicitly avoid allowing another app to call it.
        -->
        <service
            android:name="com.google.firebase.messaging.FirebaseMessagingService"
            android:directBootAware="true"
            android:exported="false" >
            <intent-filter android:priority="-500" >
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="com.elewashy.egyfilm.firebaseinitprovider"
            android:directBootAware="true"
            android:exported="false"
            android:initOrder="100" />

        <activity
            android:name="androidx.activity.ComponentActivity"
            android:exported="true" />
        <activity
            android:name="androidx.compose.ui.tooling.PreviewActivity"
            android:exported="true" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.elewashy.egyfilm.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" />
        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>