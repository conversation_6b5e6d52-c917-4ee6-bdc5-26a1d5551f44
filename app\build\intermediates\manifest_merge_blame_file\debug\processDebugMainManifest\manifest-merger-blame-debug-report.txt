1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.elewashy.egyfilm"
4    android:versionCode="1"
5    android:versionName="1.0.8" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
11-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:5:5-79
11-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:5:22-76
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission
13-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:7:5-108
14        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
14-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:7:22-78
15        android:maxSdkVersion="28" />
15-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:7:79-105
16    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
16-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:8:5-80
16-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:8:22-77
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
17-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:9:5-77
17-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:9:22-74
18    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:10:5-81
18-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:10:22-79
19    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
19-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:11:5-80
19-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:11:22-78
20    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- General permission -->
20-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:12:5-76
20-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:12:22-74
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" /> <!-- Specific permission for dataSync type -->
21-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:13:5-86
21-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:13:22-84
22    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> <!-- For installing APK files -->
22-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:14:5-82
22-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:14:22-80
23    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES" />
23-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:15:5-81
23-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:15:22-79
24    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
24-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
24-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:22-65
25    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
25-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
25-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
26
27    <permission
27-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
28        android:name="com.elewashy.egyfilm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
28-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
29        android:protectionLevel="signature" />
29-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
30
31    <uses-permission android:name="com.elewashy.egyfilm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- For managing packages -->
31-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
31-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
32    <application
32-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:17:5-115:19
33        android:allowBackup="true"
33-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:18:9-35
34        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
34-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\012d12faf62837152fa164df8f2637b0\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
35        android:debuggable="true"
36        android:extractNativeLibs="true"
37        android:hardwareAccelerated="true"
37-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:25:9-43
38        android:icon="@mipmap/ic_launcher"
38-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:19:9-43
39        android:label="@string/app_name"
39-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:20:9-41
40        android:networkSecurityConfig="@xml/network_security_config"
40-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:24:9-69
41        android:roundIcon="@mipmap/ic_launcher_round"
41-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:21:9-54
42        android:supportsRtl="true"
42-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:22:9-35
43        android:testOnly="true"
44        android:theme="@style/Theme.egyfilm" >
44-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:23:9-45
45        <activity
45-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:29:9-37:20
46            android:name="com.elewashy.egyfilm.activity.SplashActivity"
46-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:30:13-52
47            android:exported="true"
47-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:31:13-36
48            android:theme="@style/Theme.egyfilm.NoActionBar.Fullscreen" >
48-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:32:13-72
49            <intent-filter>
49-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:33:13-36:29
50                <action android:name="android.intent.action.MAIN" />
50-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:34:17-69
50-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:34:25-66
51
52                <category android:name="android.intent.category.LAUNCHER" />
52-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:35:17-77
52-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:35:27-74
53            </intent-filter>
54        </activity>
55        <activity
55-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:39:9-43:46
56            android:name="com.elewashy.egyfilm.activity.MainActivity"
56-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:40:13-50
57            android:configChanges="uiMode|keyboard|keyboardHidden|screenSize|screenLayout|smallestScreenSize|orientation"
57-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:41:13-122
58            android:exported="true"
58-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:42:13-36
59            android:launchMode="singleTop" /> <!-- Add launchMode singleTop -->
59-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:43:13-43
60
61
62        <!-- Declare the Download Activity -->
63        <activity
63-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:46:9-55:20
64            android:name="com.elewashy.egyfilm.activity.DownloadActivity"
64-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:47:13-54
65            android:exported="false"
65-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:48:13-37
66            android:label="Downloads"
66-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:49:13-38
67            android:parentActivityName="com.elewashy.egyfilm.activity.MainActivity"
67-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:50:13-64
68            android:theme="@style/Theme.egyfilm" > <!-- Use the base theme which inherits NoActionBar -->
68-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:51:13-49
69            <meta-data
69-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:52:13-54:36
70                android:name="android.app.lib_name"
70-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:53:17-52
71                android:value="" />
71-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:54:17-33
72        </activity>
73
74        <!-- Declare the Settings Activity -->
75        <activity
75-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:58:9-67:20
76            android:name="com.elewashy.egyfilm.activity.SettingsActivity"
76-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:59:13-54
77            android:exported="false"
77-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:60:13-37
78            android:label="@string/settings"
78-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:61:13-45
79            android:parentActivityName="com.elewashy.egyfilm.activity.MainActivity"
79-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:62:13-64
80            android:theme="@style/Theme.egyfilm" >
80-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:63:13-49
81            <meta-data
81-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:52:13-54:36
82                android:name="android.app.lib_name"
82-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:53:17-52
83                android:value="" />
83-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:54:17-33
84        </activity>
85
86        <!-- Declare the Filter Updates Activity -->
87        <activity
87-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:70:9-79:20
88            android:name="com.elewashy.egyfilm.activity.FilterUpdatesActivity"
88-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:71:13-59
89            android:exported="false"
89-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:72:13-37
90            android:label="@string/filter_updates"
90-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:73:13-51
91            android:parentActivityName="com.elewashy.egyfilm.activity.SettingsActivity"
91-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:74:13-68
92            android:theme="@style/Theme.egyfilm" >
92-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:75:13-49
93            <meta-data
93-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:52:13-54:36
94                android:name="android.app.lib_name"
94-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:53:17-52
95                android:value="" />
95-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:54:17-33
96        </activity>
97
98        <!-- Declare the Download Service -->
99        <service
99-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:82:9-85:56
100            android:name="com.elewashy.egyfilm.service.DownloadService"
100-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:83:13-52
101            android:exported="false"
101-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:84:13-37
102            android:foregroundServiceType="dataSync" />
102-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:85:13-53
103
104        <!-- Firebase Messaging Service -->
105        <service
105-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:88:9-94:19
106            android:name="com.elewashy.egyfilm.service.MyFirebaseMessagingService"
106-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:89:13-63
107            android:exported="false" >
107-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:90:13-37
108            <intent-filter>
108-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:91:13-93:29
109                <action android:name="com.google.firebase.MESSAGING_EVENT" />
109-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:92:17-78
109-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:92:25-75
110            </intent-filter>
111        </service>
112
113        <!-- Firebase default notification channel -->
114        <meta-data
114-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:97:9-99:54
115            android:name="com.google.firebase.messaging.default_notification_icon"
115-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:98:13-83
116            android:resource="@mipmap/ic_launcher" />
116-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:99:13-51
117        <meta-data
117-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:100:9-102:53
118            android:name="com.google.firebase.messaging.default_notification_channel_id"
118-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:101:13-89
119            android:value="egyfilm_notifications" />
119-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:102:13-50
120
121        <!-- FileProvider Declaration -->
122        <provider
123            android:name="androidx.core.content.FileProvider"
123-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:106:13-62
124            android:authorities="com.elewashy.egyfilm.provider"
124-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:107:13-60
125            android:exported="false"
125-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:108:13-37
126            android:grantUriPermissions="true" >
126-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:109:13-47
127            <meta-data
127-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:110:13-112:58
128                android:name="android.support.FILE_PROVIDER_PATHS"
128-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:111:17-67
129                android:resource="@xml/provider_paths" />
129-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:112:17-55
130        </provider>
131
132        <receiver
132-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
133            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
133-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
134            android:exported="true"
134-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
135            android:permission="com.google.android.c2dm.permission.SEND" >
135-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
136            <intent-filter>
136-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
137                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
137-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
137-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
138            </intent-filter>
139
140            <meta-data
140-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
141                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
141-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
142                android:value="true" />
142-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
143        </receiver>
144        <!--
145             FirebaseMessagingService performs security checks at runtime,
146             but set to not exported to explicitly avoid allowing another app to call it.
147        -->
148        <service
148-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
149            android:name="com.google.firebase.messaging.FirebaseMessagingService"
149-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
150            android:directBootAware="true"
150-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
151            android:exported="false" >
151-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
152            <intent-filter android:priority="-500" >
152-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:91:13-93:29
153                <action android:name="com.google.firebase.MESSAGING_EVENT" />
153-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:92:17-78
153-->C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:92:25-75
154            </intent-filter>
155        </service>
156        <service
156-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
157            android:name="com.google.firebase.components.ComponentDiscoveryService"
157-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
158            android:directBootAware="true"
158-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
159            android:exported="false" >
159-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
160            <meta-data
160-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
161                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
161-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
162                android:value="com.google.firebase.components.ComponentRegistrar" />
162-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
163            <meta-data
163-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
164                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
164-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
165                android:value="com.google.firebase.components.ComponentRegistrar" />
165-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\49cba4c6a88f35e5c76c4f6c9af0ac0c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
166            <meta-data
166-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
167                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
167-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
168                android:value="com.google.firebase.components.ComponentRegistrar" />
168-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
169            <meta-data
169-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
170                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
170-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
171                android:value="com.google.firebase.components.ComponentRegistrar" />
171-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3fe1c0eaefc22308edb62daa49299a4\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
172            <meta-data
172-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e06136e29805ff114ddb0183f6fec56b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
173                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
173-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e06136e29805ff114ddb0183f6fec56b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
174                android:value="com.google.firebase.components.ComponentRegistrar" />
174-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e06136e29805ff114ddb0183f6fec56b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
175            <meta-data
175-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
176                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
176-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
177                android:value="com.google.firebase.components.ComponentRegistrar" />
177-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
178            <meta-data
178-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\56fd1577e407ef7ee5ae30abba338964\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
179                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
179-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\56fd1577e407ef7ee5ae30abba338964\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
180                android:value="com.google.firebase.components.ComponentRegistrar" />
180-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\56fd1577e407ef7ee5ae30abba338964\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
181        </service>
182
183        <activity
183-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d15897909ae325022c6fc698bcd47cb0\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
184            android:name="com.google.android.gms.common.api.GoogleApiActivity"
184-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d15897909ae325022c6fc698bcd47cb0\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
185            android:exported="false"
185-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d15897909ae325022c6fc698bcd47cb0\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
186            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
186-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d15897909ae325022c6fc698bcd47cb0\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
187
188        <provider
188-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
189            android:name="com.google.firebase.provider.FirebaseInitProvider"
189-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
190            android:authorities="com.elewashy.egyfilm.firebaseinitprovider"
190-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
191            android:directBootAware="true"
191-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
192            android:exported="false"
192-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
193            android:initOrder="100" />
193-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2ff49c5573fa55514f1fc75b43a1b6fd\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
194
195        <activity
195-->[androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1f6943cae19452808ea696db8488b38a\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:23:9-25:39
196            android:name="androidx.activity.ComponentActivity"
196-->[androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1f6943cae19452808ea696db8488b38a\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:24:13-63
197            android:exported="true" />
197-->[androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1f6943cae19452808ea696db8488b38a\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:25:13-36
198        <activity
198-->[androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f9749fe67cbf8b0246a870c71bed68b\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
199            android:name="androidx.compose.ui.tooling.PreviewActivity"
199-->[androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f9749fe67cbf8b0246a870c71bed68b\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
200            android:exported="true" />
200-->[androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f9749fe67cbf8b0246a870c71bed68b\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
201
202        <provider
202-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
203            android:name="androidx.startup.InitializationProvider"
203-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
204            android:authorities="com.elewashy.egyfilm.androidx-startup"
204-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
205            android:exported="false" >
205-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
206            <meta-data
206-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
207                android:name="androidx.emoji2.text.EmojiCompatInitializer"
207-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
208                android:value="androidx.startup" />
208-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\370bf3ed9d9eeeef8962a570940f0ee8\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
209            <meta-data
209-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fe8bc492cd3f0f79002893094785ec38\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
210                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
210-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fe8bc492cd3f0f79002893094785ec38\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
211                android:value="androidx.startup" />
211-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fe8bc492cd3f0f79002893094785ec38\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
212            <meta-data
212-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
213                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
213-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
214                android:value="androidx.startup" />
214-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
215        </provider>
216
217        <meta-data
217-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c608acef2bf627cb353c1a031e44556d\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
218            android:name="com.google.android.gms.version"
218-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c608acef2bf627cb353c1a031e44556d\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
219            android:value="@integer/google_play_services_version" />
219-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c608acef2bf627cb353c1a031e44556d\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
220
221        <service
221-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
222            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
222-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
223            android:exported="false" >
223-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
224            <meta-data
224-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
225                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
225-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
226                android:value="cct" />
226-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5ee88db513126103d33a221d8804008\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
227        </service>
228        <service
228-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
229            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
229-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
230            android:exported="false"
230-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
231            android:permission="android.permission.BIND_JOB_SERVICE" >
231-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
232        </service>
233
234        <receiver
234-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
235            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
235-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
236            android:exported="false" />
236-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc0a9f61b2e09546dc05052055f30f0f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
237        <receiver
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
238            android:name="androidx.profileinstaller.ProfileInstallReceiver"
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
239            android:directBootAware="false"
239-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
240            android:enabled="true"
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
241            android:exported="true"
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
242            android:permission="android.permission.DUMP" >
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
243            <intent-filter>
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
244                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
245            </intent-filter>
246            <intent-filter>
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
247                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
248            </intent-filter>
249            <intent-filter>
249-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
250                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
250-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
250-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
251            </intent-filter>
252            <intent-filter>
252-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
253                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
253-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
253-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ee4d8ac766470e7c1983be978b8fc20\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
254            </intent-filter>
255        </receiver>
256    </application>
257
258</manifest>
